import 'package:flutter/material.dart';
import 'package:firebase_ui_auth/firebase_ui_auth.dart';
import 'package:firebase_ui_oauth_google/firebase_ui_oauth_google.dart';
import 'package:firebase_auth/firebase_auth.dart' hide EmailAuthProvider;
import 'package:upshift/pages/main_navigation.dart';
import 'package:upshift/pages/onboarding.dart';
import '../services/firestore.dart';
import '../models/models.dart' as models;
import '../theme/theme.dart';

class LoginPage extends StatelessWidget {
  const LoginPage({super.key});

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<User?>(
      stream: FirebaseAuth.instance.authStateChanges(),
      builder: (context, snapshot) {
        // If user is logged in
        if (snapshot.hasData) {
          return AuthenticatedUserHandler(user: snapshot.data!);
        }
        // If user is NOT logged in
        else {
          return const FirebaseUIAuthScreen();
        }
      },
    );
  }
}

class AuthenticatedUserHandler extends StatefulWidget {
  final User user;

  const AuthenticatedUserHandler({super.key, required this.user});

  @override
  State<AuthenticatedUserHandler> createState() =>
      _AuthenticatedUserHandlerState();
}

class _AuthenticatedUserHandlerState extends State<AuthenticatedUserHandler> {
  @override
  Widget build(BuildContext context) {
    return FutureBuilder<models.User?>(
      future: _checkUserOnboardingStatus(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Scaffold(
            body: Center(child: CircularProgressIndicator()),
          );
        }

        if (snapshot.hasError) {
          return Scaffold(
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    AppIcons.error,
                    size: AppDimensions.iconXxl,
                    color: AppColors.error,
                  ),
                  SizedBox(height: AppDimensions.spacingM),
                  Text('Error: ${snapshot.error}'),
                  SizedBox(height: AppDimensions.spacingM),
                  ElevatedButton(
                    onPressed: () => setState(() {}),
                    child: const Text('Retry'),
                  ),
                ],
              ),
            ),
          );
        }

        final user = snapshot.data;

        // If user doesn't exist in Firestore or is not onboarded, show onboarding
        if (user == null || !user.isOnboarded) {
          return const OnboardingPage();
        }

        // User exists and is onboarded, show main app
        return const MainNavigationPage();
      },
    );
  }

  Future<models.User?> _checkUserOnboardingStatus() async {
    try {
      // First, try to get the user from Firestore
      final firestoreUser = await FirestoreService.getUser(widget.user.uid);

      if (firestoreUser == null) {
        // User doesn't exist in Firestore, create them
        final newUser = models.User(
          id: widget.user.uid,
          name:
              widget.user.displayName ??
              widget.user.email?.split('@').first ??
              'User',
          email: widget.user.email ?? '',
          isOnboarded: false,
          isAdmin: false, // Default to non-admin
          description: null,
          preferredPersonaIds: const [],
          createdAt: DateTime.now(),
        );

        await FirestoreService.createOrUpdateUser(newUser);
        return newUser;
      }

      return firestoreUser;
    } catch (e) {
      throw Exception('Failed to check user status: $e');
    }
  }
}

class FirebaseUIAuthScreen extends StatelessWidget {
  const FirebaseUIAuthScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return SignInScreen(
      providers: [
        EmailAuthProvider(),
        GoogleProvider(
          clientId:
              '49879574938-k2mk9104v7uo81elbfrn5e71qoa1bm3p.apps.googleusercontent.com',
        ),
      ],
      headerBuilder: (context, constraints, _) {
        return Padding(
          padding: const EdgeInsets.all(20),
          child: Center(
            child: Text(
              'Upshift',
              style: Theme.of(context).textTheme.headlineLarge,
            ),
          ),
        );
      },
      subtitleBuilder: (context, action) {
        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 8.0),
          child: action == AuthAction.signIn
              ? const Text('Welcome back to Upshift!')
              : const Text('Join Upshift today!'),
        );
      },
      footerBuilder: (context, action) {
        return const Padding(
          padding: EdgeInsets.only(top: 16),
          child: Text(
            'By signing in, you agree to our terms and conditions.',
            style: TextStyle(color: Colors.grey),
          ),
        );
      },
    );
  }
}
